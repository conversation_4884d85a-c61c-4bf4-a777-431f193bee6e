import React from 'react';
import {
  BookOpenIcon,
  BeakerIcon,
  LanguageIcon,
  PaintBrushIcon,
  CalculatorIcon,
  HeartIcon
} from '@heroicons/react/24/outline';

const Programs: React.FC = () => {
  const programs = [
    {
      icon: HeartIcon,
      title: 'Nursery Education',
      level: 'Ages 3-5',
      description: 'Early childhood development with focus on play-based learning, social skills, and school readiness.',
      subjects: ['Pre-literacy Skills', 'Basic Numeracy', 'Creative Play', 'Social Development', 'Motor Skills', 'Art & Craft']
    },
    {
      icon: BookOpenIcon,
      title: 'Primary Education',
      level: 'Grades 1-6',
      description: 'Comprehensive foundation learning with focus on literacy, numeracy, and essential life skills.',
      subjects: ['Mathematics', 'English', 'Kinyarwanda', 'Science', 'Social Studies', 'Physical Education']
    },
    {
      icon: LanguageIcon,
      title: 'Language Development',
      level: 'Nursery - Primary',
      description: 'Strong multilingual foundation preparing students for Rwanda\'s trilingual education system.',
      subjects: ['English', 'French', 'Kinyarwanda', 'Reading Comprehension', 'Writing Skills', 'Oral Communication']
    },
    {
      icon: PaintBrushIcon,
      title: 'Creative Arts',
      level: 'Nursery - Primary',
      description: 'Nurturing creativity and cultural appreciation through various artistic expressions.',
      subjects: ['Visual Arts', 'Music', 'Traditional Dance', 'Drama', 'Crafts', 'Cultural Studies']
    },
    {
      icon: BeakerIcon,
      title: 'Science & Discovery',
      level: 'Primary',
      description: 'Introduction to scientific thinking and exploration of the natural world.',
      subjects: ['Basic Science', 'Nature Studies', 'Simple Experiments', 'Environmental Awareness', 'Health Education']
    },
    {
      icon: CalculatorIcon,
      title: 'Mathematics Foundation',
      level: 'Nursery - Primary',
      description: 'Building strong mathematical foundations through practical and engaging methods.',
      subjects: ['Number Recognition', 'Basic Operations', 'Problem Solving', 'Geometry', 'Measurement', 'Logic']
    }
  ];

  const facilities = [
    'Bright and Colorful Classrooms',
    'Safe Playground Areas',
    'Children\'s Library Corner',
    'Art and Craft Rooms',
    'Music and Movement Space',
    'Nutritious Meal Program'
  ];

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-primary-600 to-primary-800 text-white py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-4xl md:text-5xl font-heading font-bold mb-6">
              Academic Programs
            </h1>
            <p className="text-xl md:text-2xl max-w-3xl mx-auto">
              Specialized Nursery and Primary education programs designed to nurture
              young minds and build strong foundations for lifelong learning.
            </p>
          </div>
        </div>
      </section>

      {/* Programs Overview */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-heading font-bold text-secondary-900 mb-4">
              Our Educational Programs
            </h2>
            <p className="text-lg text-secondary-600 max-w-2xl mx-auto">
              We specialize in Nursery and Primary education, providing a strong foundation
              for young learners to build upon as they continue their educational journey.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {programs.map((program, index) => (
              <div 
                key={index}
                className="bg-white border border-secondary-200 rounded-xl p-6 hover:shadow-lg transition-shadow duration-300"
              >
                <div className="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center mb-4">
                  <program.icon className="h-6 w-6 text-primary-600" />
                </div>
                <div className="mb-4">
                  <h3 className="text-xl font-semibold text-secondary-900 mb-2">
                    {program.title}
                  </h3>
                  <span className="inline-block bg-primary-100 text-primary-800 text-sm font-medium px-3 py-1 rounded-full">
                    {program.level}
                  </span>
                </div>
                <p className="text-secondary-600 mb-4">
                  {program.description}
                </p>
                <div>
                  <h4 className="font-semibold text-secondary-900 mb-2">Key Subjects:</h4>
                  <div className="flex flex-wrap gap-2">
                    {program.subjects.map((subject, subIndex) => (
                      <span 
                        key={subIndex}
                        className="bg-secondary-100 text-secondary-700 text-xs px-2 py-1 rounded"
                      >
                        {subject}
                      </span>
                    ))}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Curriculum Highlights */}
      <section className="py-16 bg-secondary-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-3xl md:text-4xl font-heading font-bold text-secondary-900 mb-6">
                Curriculum Highlights
              </h2>
              <div className="space-y-4">
                <div className="flex items-start space-x-3">
                  <div className="w-2 h-2 bg-primary-600 rounded-full mt-2 flex-shrink-0"></div>
                  <p className="text-secondary-700">
                    <strong>Play-Based Learning:</strong> Age-appropriate learning through play,
                    exploration, and hands-on activities for nursery students.
                  </p>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="w-2 h-2 bg-primary-600 rounded-full mt-2 flex-shrink-0"></div>
                  <p className="text-secondary-700">
                    <strong>Foundation Skills:</strong> Strong emphasis on literacy, numeracy,
                    and essential life skills for primary students.
                  </p>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="w-2 h-2 bg-primary-600 rounded-full mt-2 flex-shrink-0"></div>
                  <p className="text-secondary-700">
                    <strong>Character Development:</strong> Integration of moral values and
                    discipline throughout all learning activities.
                  </p>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="w-2 h-2 bg-primary-600 rounded-full mt-2 flex-shrink-0"></div>
                  <p className="text-secondary-700">
                    <strong>Small Class Sizes:</strong> Personalized attention with low
                    student-to-teacher ratios for optimal learning.
                  </p>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="w-2 h-2 bg-primary-600 rounded-full mt-2 flex-shrink-0"></div>
                  <p className="text-secondary-700">
                    <strong>Trilingual Foundation:</strong> Early introduction to English, French,
                    and Kinyarwanda to prepare for Rwanda's education system.
                  </p>
                </div>
              </div>
            </div>
            
            <div className="bg-white p-8 rounded-xl shadow-lg">
              <h3 className="text-2xl font-heading font-bold text-secondary-900 mb-6">
                School Facilities
              </h3>
              <ul className="space-y-3">
                {facilities.map((facility, index) => (
                  <li key={index} className="flex items-center space-x-3">
                    <div className="w-2 h-2 bg-primary-600 rounded-full"></div>
                    <span className="text-secondary-700">{facility}</span>
                  </li>
                ))}
              </ul>
            </div>
          </div>
        </div>
      </section>

      {/* Extracurricular Activities */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-heading font-bold text-secondary-900 mb-4">
              Beyond the Classroom
            </h2>
            <p className="text-lg text-secondary-600 max-w-2xl mx-auto">
              We believe in holistic development through age-appropriate activities that nurture creativity, physical development, and social skills.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl">🏃‍♂️</span>
              </div>
              <h3 className="text-xl font-semibold text-secondary-900 mb-3">Physical Activities</h3>
              <p className="text-secondary-600">
                Age-appropriate games, playground activities, basic sports skills, and movement exercises for healthy development.
              </p>
            </div>
            
            <div className="text-center">
              <div className="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl">🎭</span>
              </div>
              <h3 className="text-xl font-semibold text-secondary-900 mb-3">Creative Arts</h3>
              <p className="text-secondary-600">
                Drawing, painting, singing, simple musical instruments, storytelling, and traditional Rwandan cultural activities.
              </p>
            </div>
            
            <div className="text-center">
              <div className="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl">🤝</span>
              </div>
              <h3 className="text-xl font-semibold text-secondary-900 mb-3">Life Skills</h3>
              <p className="text-secondary-600">
                Basic hygiene, sharing, helping others, environmental awareness, and developing good habits and manners.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-primary-600 text-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl md:text-4xl font-heading font-bold mb-6">
            Ready to Enroll?
          </h2>
          <p className="text-xl text-primary-100 mb-8">
            Join our community of learners and discover your potential at La Promise School.
          </p>
          <a
            href="/contact"
            className="bg-white text-primary-600 px-8 py-3 rounded-lg font-semibold hover:bg-primary-50 transition-colors duration-200 shadow-lg inline-block"
          >
            Contact Us for Enrollment
          </a>
        </div>
      </section>
    </div>
  );
};

export default Programs;
