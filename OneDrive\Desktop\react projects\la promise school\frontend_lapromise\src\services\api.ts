import axios from 'axios';

// Create axios instance with base configuration
const api = axios.create({
  baseURL: process.env.REACT_APP_API_URL || 'http://localhost:5000/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor for error handling
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // Handle unauthorized access
      localStorage.removeItem('token');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

// API service functions
export const apiService = {
  // Health check
  healthCheck: () => api.get('/health'),

  // Announcements
  getAnnouncements: (params?: { limit?: number; offset?: number; target_audience?: string }) =>
    api.get('/announcements', { params }),
  
  getAnnouncement: (id: string) => api.get(`/announcements/${id}`),
  
  createAnnouncement: (data: any) => api.post('/announcements', data),
  
  updateAnnouncement: (id: string, data: any) => api.put(`/announcements/${id}`, data),
  
  deleteAnnouncement: (id: string) => api.delete(`/announcements/${id}`),

  // Contact
  submitContactForm: (data: {
    name: string;
    email: string;
    phone?: string;
    subject: string;
    message: string;
  }) => api.post('/contact', data),
  
  getContactMessages: (params?: { status?: string; limit?: number; offset?: number }) =>
    api.get('/contact', { params }),
  
  getContactMessage: (id: string) => api.get(`/contact/${id}`),
  
  updateContactMessageStatus: (id: string, status: string) =>
    api.put(`/contact/${id}/status`, { status }),
  
  deleteContactMessage: (id: string) => api.delete(`/contact/${id}`),
  
  getContactStats: () => api.get('/contact/stats/overview'),

  // Students
  getStudents: (params?: { class_level?: string; status?: string; limit?: number; offset?: number }) =>
    api.get('/students', { params }),
  
  getStudent: (id: string) => api.get(`/students/${id}`),
  
  createStudent: (data: any) => api.post('/students', data),
  
  updateStudent: (id: string, data: any) => api.put(`/students/${id}`, data),
  
  deleteStudent: (id: string) => api.delete(`/students/${id}`),
  
  getStudentStats: () => api.get('/students/stats/overview'),

  // Teachers
  getTeachers: (params?: { status?: string; limit?: number; offset?: number }) =>
    api.get('/teachers', { params }),
  
  getTeacher: (id: string) => api.get(`/teachers/${id}`),
  
  createTeacher: (data: any) => api.post('/teachers', data),
  
  updateTeacher: (id: string, data: any) => api.put(`/teachers/${id}`, data),
  
  deleteTeacher: (id: string) => api.delete(`/teachers/${id}`),

  // Courses
  getCourses: (params?: { class_level?: string; status?: string; limit?: number; offset?: number }) =>
    api.get('/courses', { params }),
  
  getCourse: (id: string) => api.get(`/courses/${id}`),
  
  createCourse: (data: any) => api.post('/courses', data),
  
  updateCourse: (id: string, data: any) => api.put(`/courses/${id}`, data),
  
  deleteCourse: (id: string) => api.delete(`/courses/${id}`),

  // Gallery
  getGallery: (params?: { category?: string; limit?: number; offset?: number }) =>
    api.get('/gallery', { params }),
  
  getGalleryItem: (id: string) => api.get(`/gallery/${id}`),
  
  createGalleryItem: (data: any) => api.post('/gallery', data),
  
  updateGalleryItem: (id: string, data: any) => api.put(`/gallery/${id}`, data),
  
  deleteGalleryItem: (id: string) => api.delete(`/gallery/${id}`),

  // Authentication
  login: (credentials: { email: string; password: string }) =>
    api.post('/auth/login', credentials),
  
  register: (userData: { username: string; email: string; password: string; role?: string }) =>
    api.post('/auth/register', userData),
  
  getProfile: () => api.get('/auth/profile'),
  
  changePassword: (passwords: { currentPassword: string; newPassword: string }) =>
    api.put('/auth/change-password', passwords),
};

export default api;
