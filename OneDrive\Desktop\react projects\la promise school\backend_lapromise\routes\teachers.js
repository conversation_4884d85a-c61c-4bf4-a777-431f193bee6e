const express = require('express');
const { pool } = require('../config/database');
const router = express.Router();

// Get all teachers
router.get('/', async (req, res) => {
  try {
    const { status = 'active', limit = 50, offset = 0 } = req.query;

    const [teachers] = await pool.execute(`
      SELECT t.*, u.username 
      FROM teachers t 
      LEFT JOIN users u ON t.user_id = u.id 
      WHERE t.status = ? 
      ORDER BY t.last_name, t.first_name 
      LIMIT ? OFFSET ?
    `, [status, parseInt(limit), parseInt(offset)]);

    const [countResult] = await pool.execute(
      'SELECT COUNT(*) as total FROM teachers WHERE status = ?',
      [status]
    );

    res.json({
      teachers,
      total: countResult[0].total,
      limit: parseInt(limit),
      offset: parseInt(offset)
    });

  } catch (error) {
    console.error('Error fetching teachers:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Get single teacher by ID
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;

    const [teachers] = await pool.execute(`
      SELECT t.*, u.username 
      FROM teachers t 
      LEFT JOIN users u ON t.user_id = u.id 
      WHERE t.id = ?
    `, [id]);

    if (teachers.length === 0) {
      return res.status(404).json({ message: 'Teacher not found' });
    }

    res.json({ teacher: teachers[0] });

  } catch (error) {
    console.error('Error fetching teacher:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Create new teacher
router.post('/', async (req, res) => {
  try {
    const {
      teacher_id,
      first_name,
      last_name,
      email,
      phone,
      subject_specialization,
      qualification,
      experience_years,
      hire_date
    } = req.body;

    if (!teacher_id || !first_name || !last_name || !email) {
      return res.status(400).json({ 
        message: 'Teacher ID, first name, last name, and email are required' 
      });
    }

    // Check if teacher_id or email already exists
    const [existing] = await pool.execute(
      'SELECT id FROM teachers WHERE teacher_id = ? OR email = ?',
      [teacher_id, email]
    );

    if (existing.length > 0) {
      return res.status(400).json({ message: 'Teacher ID or email already exists' });
    }

    const [result] = await pool.execute(`
      INSERT INTO teachers 
      (teacher_id, first_name, last_name, email, phone, subject_specialization, 
       qualification, experience_years, hire_date) 
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
    `, [
      teacher_id,
      first_name,
      last_name,
      email,
      phone,
      subject_specialization,
      qualification,
      experience_years || 0,
      hire_date || new Date()
    ]);

    res.status(201).json({
      message: 'Teacher created successfully',
      teacher: {
        id: result.insertId,
        teacher_id,
        first_name,
        last_name,
        email,
        status: 'active'
      }
    });

  } catch (error) {
    console.error('Error creating teacher:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

module.exports = router;
