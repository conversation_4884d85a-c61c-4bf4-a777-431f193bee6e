const express = require('express');
const { pool } = require('../config/database');
const router = express.Router();

// Get all students
router.get('/', async (req, res) => {
  try {
    const { class_level, status = 'active', limit = 50, offset = 0 } = req.query;

    let query = `
      SELECT s.*, u.username, u.email as user_email 
      FROM students s 
      LEFT JOIN users u ON s.user_id = u.id 
      WHERE s.status = ?
    `;
    const params = [status];

    if (class_level) {
      query += ' AND s.class_level = ?';
      params.push(class_level);
    }

    query += ' ORDER BY s.last_name, s.first_name LIMIT ? OFFSET ?';
    params.push(parseInt(limit), parseInt(offset));

    const [students] = await pool.execute(query, params);

    // Get total count
    let countQuery = 'SELECT COUNT(*) as total FROM students WHERE status = ?';
    const countParams = [status];

    if (class_level) {
      countQuery += ' AND class_level = ?';
      countParams.push(class_level);
    }

    const [countResult] = await pool.execute(countQuery, countParams);
    const total = countResult[0].total;

    res.json({
      students,
      total,
      limit: parseInt(limit),
      offset: parseInt(offset)
    });

  } catch (error) {
    console.error('Error fetching students:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Get single student by ID
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;

    const [students] = await pool.execute(`
      SELECT s.*, u.username, u.email as user_email 
      FROM students s 
      LEFT JOIN users u ON s.user_id = u.id 
      WHERE s.id = ?
    `, [id]);

    if (students.length === 0) {
      return res.status(404).json({ message: 'Student not found' });
    }

    res.json({ student: students[0] });

  } catch (error) {
    console.error('Error fetching student:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Create new student
router.post('/', async (req, res) => {
  try {
    const {
      student_id,
      first_name,
      last_name,
      date_of_birth,
      gender,
      class_level,
      parent_name,
      parent_phone,
      parent_email,
      address,
      enrollment_date
    } = req.body;

    // Validation
    if (!student_id || !first_name || !last_name || !gender || !class_level) {
      return res.status(400).json({ 
        message: 'Student ID, first name, last name, gender, and class level are required' 
      });
    }

    // Check if student_id already exists
    const [existing] = await pool.execute(
      'SELECT id FROM students WHERE student_id = ?',
      [student_id]
    );

    if (existing.length > 0) {
      return res.status(400).json({ message: 'Student ID already exists' });
    }

    const [result] = await pool.execute(`
      INSERT INTO students 
      (student_id, first_name, last_name, date_of_birth, gender, class_level, 
       parent_name, parent_phone, parent_email, address, enrollment_date) 
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `, [
      student_id,
      first_name,
      last_name,
      date_of_birth,
      gender,
      class_level,
      parent_name,
      parent_phone,
      parent_email,
      address,
      enrollment_date || new Date()
    ]);

    res.status(201).json({
      message: 'Student created successfully',
      student: {
        id: result.insertId,
        student_id,
        first_name,
        last_name,
        class_level,
        status: 'active'
      }
    });

  } catch (error) {
    console.error('Error creating student:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Update student
router.put('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const updateFields = req.body;

    // Check if student exists
    const [existing] = await pool.execute(
      'SELECT id FROM students WHERE id = ?',
      [id]
    );

    if (existing.length === 0) {
      return res.status(404).json({ message: 'Student not found' });
    }

    // Build update query dynamically
    const updates = [];
    const params = [];

    const allowedFields = [
      'first_name', 'last_name', 'date_of_birth', 'gender', 'class_level',
      'parent_name', 'parent_phone', 'parent_email', 'address', 'status'
    ];

    for (const field of allowedFields) {
      if (updateFields[field] !== undefined) {
        updates.push(`${field} = ?`);
        params.push(updateFields[field]);
      }
    }

    if (updates.length === 0) {
      return res.status(400).json({ message: 'No valid fields to update' });
    }

    params.push(id);

    await pool.execute(
      `UPDATE students SET ${updates.join(', ')}, updated_at = CURRENT_TIMESTAMP WHERE id = ?`,
      params
    );

    res.json({ message: 'Student updated successfully' });

  } catch (error) {
    console.error('Error updating student:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Delete student
router.delete('/:id', async (req, res) => {
  try {
    const { id } = req.params;

    const [result] = await pool.execute(
      'DELETE FROM students WHERE id = ?',
      [id]
    );

    if (result.affectedRows === 0) {
      return res.status(404).json({ message: 'Student not found' });
    }

    res.json({ message: 'Student deleted successfully' });

  } catch (error) {
    console.error('Error deleting student:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Get students statistics
router.get('/stats/overview', async (req, res) => {
  try {
    const [stats] = await pool.execute(`
      SELECT 
        COUNT(*) as total_students,
        SUM(CASE WHEN status = 'active' THEN 1 ELSE 0 END) as active_students,
        SUM(CASE WHEN status = 'inactive' THEN 1 ELSE 0 END) as inactive_students,
        SUM(CASE WHEN status = 'graduated' THEN 1 ELSE 0 END) as graduated_students,
        SUM(CASE WHEN gender = 'male' THEN 1 ELSE 0 END) as male_students,
        SUM(CASE WHEN gender = 'female' THEN 1 ELSE 0 END) as female_students
      FROM students
    `);

    // Get class distribution
    const [classStats] = await pool.execute(`
      SELECT class_level, COUNT(*) as count 
      FROM students 
      WHERE status = 'active' 
      GROUP BY class_level 
      ORDER BY class_level
    `);

    res.json({ 
      stats: stats[0],
      classDistribution: classStats
    });

  } catch (error) {
    console.error('Error fetching student stats:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

module.exports = router;
