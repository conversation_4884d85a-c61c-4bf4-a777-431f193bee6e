{"name": "backend_lapromise", "version": "1.0.0", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "seed": "node seeders/sampleData.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["school", "education", "api", "rwanda"], "author": "La Promise School", "license": "ISC", "description": "Backend API for La Promise School website - Gisenyi, Rubavu, Rwanda", "dependencies": {"bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^17.2.0", "express": "^4.18.2", "express-rate-limit": "^8.0.1", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "multer": "^2.0.2", "mysql2": "^3.14.2"}, "devDependencies": {"nodemon": "^3.1.10"}}