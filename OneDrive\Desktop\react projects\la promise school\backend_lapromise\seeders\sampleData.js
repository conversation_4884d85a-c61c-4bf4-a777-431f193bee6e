const { pool } = require('../config/database');
const bcrypt = require('bcryptjs');

const seedDatabase = async () => {
  try {
    console.log('🌱 Starting database seeding...');

    // Create admin user
    const hashedPassword = await bcrypt.hash('admin123', 12);
    
    await pool.execute(`
      INSERT IGNORE INTO users (username, email, password, role) 
      VALUES ('admin', '<EMAIL>', ?, 'admin')
    `, [hashedPassword]);

    // Insert sample announcements
    const announcements = [
      {
        title: 'Welcome to New Academic Year 2024',
        content: 'We are excited to welcome all students back for the new academic year. Classes begin on January 15th, 2024. Please ensure all registration requirements are completed.',
        target_audience: 'all',
        priority: 'high'
      },
      {
        title: 'Science Fair Competition',
        content: 'Our annual science fair will be held on March 20th, 2024. Students from all levels are encouraged to participate and showcase their innovative projects.',
        target_audience: 'students',
        priority: 'medium'
      },
      {
        title: 'Parent-Teacher Conference',
        content: 'Parent-teacher conferences are scheduled for February 10-12, 2024. Please contact the school office to schedule your appointment.',
        target_audience: 'parents',
        priority: 'high'
      },
      {
        title: 'New Library Resources Available',
        content: 'We have added over 500 new books to our library collection, including digital resources and online databases for student research.',
        target_audience: 'all',
        priority: 'medium'
      },
      {
        title: 'Sports Day 2024',
        content: 'Join us for our annual sports day on April 15th, 2024. Various competitions will be held including athletics, football, basketball, and traditional games.',
        target_audience: 'all',
        priority: 'medium'
      }
    ];

    for (const announcement of announcements) {
      await pool.execute(`
        INSERT INTO announcements (title, content, author_id, target_audience, priority, publish_date) 
        VALUES (?, ?, 1, ?, ?, NOW())
      `, [announcement.title, announcement.content, announcement.target_audience, announcement.priority]);
    }

    // Insert sample teachers
    const teachers = [
      {
        teacher_id: 'TCH001',
        first_name: 'Jean',
        last_name: 'Uwimana',
        email: '<EMAIL>',
        phone: '+250 788 111 001',
        subject_specialization: 'Mathematics',
        qualification: 'Master of Science in Mathematics',
        experience_years: 8
      },
      {
        teacher_id: 'TCH002',
        first_name: 'Marie',
        last_name: 'Mukamana',
        email: '<EMAIL>',
        phone: '+250 788 111 002',
        subject_specialization: 'English Literature',
        qualification: 'Bachelor of Arts in English',
        experience_years: 6
      },
      {
        teacher_id: 'TCH003',
        first_name: 'Paul',
        last_name: 'Nzeyimana',
        email: '<EMAIL>',
        phone: '+250 788 111 003',
        subject_specialization: 'Physics',
        qualification: 'Master of Science in Physics',
        experience_years: 10
      },
      {
        teacher_id: 'TCH004',
        first_name: 'Grace',
        last_name: 'Uwamahoro',
        email: '<EMAIL>',
        phone: '+250 788 111 004',
        subject_specialization: 'Chemistry',
        qualification: 'Bachelor of Science in Chemistry',
        experience_years: 5
      },
      {
        teacher_id: 'TCH005',
        first_name: 'David',
        last_name: 'Habimana',
        email: '<EMAIL>',
        phone: '+250 788 111 005',
        subject_specialization: 'History',
        qualification: 'Master of Arts in History',
        experience_years: 12
      }
    ];

    for (const teacher of teachers) {
      await pool.execute(`
        INSERT INTO teachers (teacher_id, first_name, last_name, email, phone, subject_specialization, qualification, experience_years) 
        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
      `, [teacher.teacher_id, teacher.first_name, teacher.last_name, teacher.email, teacher.phone, teacher.subject_specialization, teacher.qualification, teacher.experience_years]);
    }

    // Insert sample courses
    const courses = [
      { course_code: 'MATH101', course_name: 'Basic Mathematics', class_level: 'Grade 7', teacher_id: 1, credits: 4 },
      { course_code: 'ENG101', course_name: 'English Language', class_level: 'Grade 7', teacher_id: 2, credits: 4 },
      { course_code: 'PHY201', course_name: 'Physics', class_level: 'Grade 10', teacher_id: 3, credits: 4 },
      { course_code: 'CHEM201', course_name: 'Chemistry', class_level: 'Grade 10', teacher_id: 4, credits: 4 },
      { course_code: 'HIST101', course_name: 'Rwanda History', class_level: 'Grade 8', teacher_id: 5, credits: 3 },
      { course_code: 'MATH301', course_name: 'Advanced Mathematics', class_level: 'Grade 12', teacher_id: 1, credits: 5 },
      { course_code: 'ENG301', course_name: 'English Literature', class_level: 'Grade 12', teacher_id: 2, credits: 4 }
    ];

    for (const course of courses) {
      await pool.execute(`
        INSERT INTO courses (course_code, course_name, class_level, teacher_id, credits) 
        VALUES (?, ?, ?, ?, ?)
      `, [course.course_code, course.course_name, course.class_level, course.teacher_id, course.credits]);
    }

    // Insert sample students
    const students = [
      {
        student_id: 'STD001',
        first_name: 'Alice',
        last_name: 'Uwimana',
        date_of_birth: '2008-03-15',
        gender: 'female',
        class_level: 'Grade 10',
        parent_name: 'John Uwimana',
        parent_phone: '+250 788 222 001',
        parent_email: '<EMAIL>'
      },
      {
        student_id: 'STD002',
        first_name: 'Bob',
        last_name: 'Nkurunziza',
        date_of_birth: '2009-07-22',
        gender: 'male',
        class_level: 'Grade 9',
        parent_name: 'Mary Nkurunziza',
        parent_phone: '+250 788 222 002',
        parent_email: '<EMAIL>'
      },
      {
        student_id: 'STD003',
        first_name: 'Claire',
        last_name: 'Mukamana',
        date_of_birth: '2007-11-08',
        gender: 'female',
        class_level: 'Grade 11',
        parent_name: 'Peter Mukamana',
        parent_phone: '+250 788 222 003',
        parent_email: '<EMAIL>'
      },
      {
        student_id: 'STD004',
        first_name: 'Daniel',
        last_name: 'Habimana',
        date_of_birth: '2008-12-03',
        gender: 'male',
        class_level: 'Grade 10',
        parent_name: 'Rose Habimana',
        parent_phone: '+250 788 222 004',
        parent_email: '<EMAIL>'
      },
      {
        student_id: 'STD005',
        first_name: 'Emma',
        last_name: 'Uwamahoro',
        date_of_birth: '2006-05-18',
        gender: 'female',
        class_level: 'Grade 12',
        parent_name: 'James Uwamahoro',
        parent_phone: '+250 788 222 005',
        parent_email: '<EMAIL>'
      }
    ];

    for (const student of students) {
      await pool.execute(`
        INSERT INTO students (student_id, first_name, last_name, date_of_birth, gender, class_level, parent_name, parent_phone, parent_email) 
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [student.student_id, student.first_name, student.last_name, student.date_of_birth, student.gender, student.class_level, student.parent_name, student.parent_phone, student.parent_email]);
    }

    // Insert sample gallery items
    const galleryItems = [
      {
        title: 'School Main Building',
        description: 'Our beautiful main academic building with modern facilities',
        image_url: 'https://images.unsplash.com/photo-1580582932707-520aed937b7b?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
        category: 'facilities'
      },
      {
        title: 'Science Laboratory',
        description: 'Students conducting experiments in our well-equipped science lab',
        image_url: 'https://images.unsplash.com/photo-1532094349884-543bc11b234d?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
        category: 'facilities'
      },
      {
        title: 'Annual Sports Day',
        description: 'Students participating in various sports activities',
        image_url: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
        category: 'events'
      },
      {
        title: 'Graduation Ceremony 2023',
        description: 'Celebrating our graduates and their achievements',
        image_url: 'https://images.unsplash.com/photo-1523050854058-8df90110c9d1?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
        category: 'events'
      }
    ];

    for (const item of galleryItems) {
      await pool.execute(`
        INSERT INTO gallery (title, description, image_url, category, uploaded_by) 
        VALUES (?, ?, ?, ?, 1)
      `, [item.title, item.description, item.image_url, item.category]);
    }

    console.log('✅ Database seeding completed successfully!');
    console.log('📊 Sample data inserted:');
    console.log('   - 1 Admin user');
    console.log('   - 5 Announcements');
    console.log('   - 5 Teachers');
    console.log('   - 7 Courses');
    console.log('   - 5 Students');
    console.log('   - 4 Gallery items');

  } catch (error) {
    console.error('❌ Error seeding database:', error);
  }
};

// Run seeder if called directly
if (require.main === module) {
  seedDatabase().then(() => {
    process.exit(0);
  });
}

module.exports = { seedDatabase };
