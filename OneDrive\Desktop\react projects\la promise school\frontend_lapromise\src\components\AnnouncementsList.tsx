import React, { useState, useEffect } from 'react';
import { apiService } from '../services/api';
import { 
  SpeakerWaveIcon, 
  CalendarDaysIcon,
  ExclamationTriangleIcon 
} from '@heroicons/react/24/outline';

interface Announcement {
  id: number;
  title: string;
  content: string;
  author_name: string;
  target_audience: string;
  priority: 'low' | 'medium' | 'high';
  publish_date: string;
  created_at: string;
}

const AnnouncementsList: React.FC = () => {
  const [announcements, setAnnouncements] = useState<Announcement[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchAnnouncements();
  }, []);

  const fetchAnnouncements = async () => {
    try {
      setLoading(true);
      const response = await apiService.getAnnouncements({ limit: 5 });
      setAnnouncements(response.data.announcements);
      setError(null);
    } catch (err) {
      console.error('Error fetching announcements:', err);
      setError('Failed to load announcements. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'low':
        return 'bg-green-100 text-green-800 border-green-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getPriorityIcon = (priority: string) => {
    switch (priority) {
      case 'high':
        return <ExclamationTriangleIcon className="h-4 w-4" />;
      default:
        return <SpeakerWaveIcon className="h-4 w-4" />;
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const truncateContent = (content: string, maxLength: number = 150) => {
    if (content.length <= maxLength) return content;
    return content.substring(0, maxLength) + '...';
  };

  if (loading) {
    return (
      <div className="bg-white rounded-xl shadow-lg p-6">
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded mb-4"></div>
          <div className="space-y-4">
            {[1, 2, 3].map((i) => (
              <div key={i} className="border border-gray-200 rounded-lg p-4">
                <div className="h-4 bg-gray-200 rounded mb-2"></div>
                <div className="h-3 bg-gray-200 rounded mb-2"></div>
                <div className="h-3 bg-gray-200 rounded w-3/4"></div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-white rounded-xl shadow-lg p-6">
        <h3 className="text-xl font-semibold text-secondary-900 mb-4 flex items-center">
          <SpeakerWaveIcon className="h-6 w-6 text-primary-600 mr-2" />
          Latest Announcements
        </h3>
        <div className="text-center py-8">
          <ExclamationTriangleIcon className="h-12 w-12 text-red-400 mx-auto mb-4" />
          <p className="text-red-600 mb-4">{error}</p>
          <button
            onClick={fetchAnnouncements}
            className="bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700 transition-colors duration-200"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-xl shadow-lg p-6">
      <h3 className="text-xl font-semibold text-secondary-900 mb-6 flex items-center">
        <SpeakerWaveIcon className="h-6 w-6 text-primary-600 mr-2" />
        Latest Announcements
      </h3>
      
      {announcements.length === 0 ? (
        <div className="text-center py-8">
          <SpeakerWaveIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <p className="text-gray-600">No announcements available at the moment.</p>
        </div>
      ) : (
        <div className="space-y-4">
          {announcements.map((announcement) => (
            <div
              key={announcement.id}
              className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow duration-200"
            >
              <div className="flex items-start justify-between mb-3">
                <h4 className="text-lg font-semibold text-secondary-900 flex-1">
                  {announcement.title}
                </h4>
                <div className="flex items-center space-x-2 ml-4">
                  <span
                    className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border ${getPriorityColor(
                      announcement.priority
                    )}`}
                  >
                    {getPriorityIcon(announcement.priority)}
                    <span className="ml-1 capitalize">{announcement.priority}</span>
                  </span>
                </div>
              </div>
              
              <p className="text-secondary-600 mb-3 leading-relaxed">
                {truncateContent(announcement.content)}
              </p>
              
              <div className="flex items-center justify-between text-sm text-secondary-500">
                <div className="flex items-center">
                  <CalendarDaysIcon className="h-4 w-4 mr-1" />
                  <span>{formatDate(announcement.publish_date)}</span>
                </div>
                <div className="flex items-center space-x-4">
                  {announcement.author_name && (
                    <span>By {announcement.author_name}</span>
                  )}
                  <span className="capitalize bg-gray-100 px-2 py-1 rounded text-xs">
                    {announcement.target_audience}
                  </span>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
      
      {announcements.length > 0 && (
        <div className="mt-6 text-center">
          <button className="text-primary-600 hover:text-primary-700 font-medium transition-colors duration-200">
            View All Announcements →
          </button>
        </div>
      )}
    </div>
  );
};

export default AnnouncementsList;
