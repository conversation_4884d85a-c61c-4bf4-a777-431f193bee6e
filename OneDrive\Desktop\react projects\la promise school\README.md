# La Promise School Website

A comprehensive school website for La Promise School located in Gisenyi, Rubavu, Rwanda. Built with React.js, Node.js, Express.js, MySQL, and Tailwind CSS.

## 🎯 School Motto
**Discipline • Work • Cleanliness**

## 🌟 Mission
At La Promise School, we are committed to shaping a brighter future for Rwanda by providing high-quality, student-centered education. We nurture learners through innovative teaching, modern resources, and a supportive environment that encourages critical thinking, creativity, and character development.

## 🏗️ Project Structure

```
Heroes gym/
├── backend_lapromise/          # Node.js/Express.js backend
│   ├── config/                 # Database configuration
│   ├── routes/                 # API routes
│   ├── seeders/               # Database seeders
│   └── server.js              # Main server file
├── frontend_lapromise/         # React.js frontend
│   ├── src/
│   │   ├── components/        # Reusable components
│   │   ├── pages/            # Page components
│   │   └── services/         # API services
│   └── public/               # Static assets
└── README.md                 # This file
```

## 🚀 Technologies Used

### Backend
- **Node.js** - Runtime environment
- **Express.js** - Web framework
- **MySQL** - Database
- **JWT** - Authentication
- **bcryptjs** - Password hashing
- **CORS** - Cross-origin resource sharing
- **Helmet** - Security middleware

### Frontend
- **React.js** - UI library
- **TypeScript** - Type safety
- **Tailwind CSS** - Styling framework
- **React Router** - Navigation
- **Axios** - HTTP client
- **Heroicons** - Icon library

## 📋 Prerequisites

Before running this project, make sure you have the following installed:
- Node.js (v16 or higher)
- MySQL (v8.0 or higher)
- npm or yarn package manager

## 🛠️ Installation & Setup

### 1. Clone the Repository
```bash
git clone <repository-url>
cd "Heroes gym"
```

### 2. Backend Setup

#### Navigate to backend directory
```bash
cd backend_lapromise
```

#### Install dependencies
```bash
npm install
```

#### Configure Environment Variables
Create a `.env` file in the backend directory with the following variables:
```env
# Server Configuration
PORT=5000
NODE_ENV=development

# Database Configuration
DB_HOST=localhost
DB_USER=root
DB_PASSWORD=your_mysql_password
DB_NAME=lapromise_school

# Frontend URL
FRONTEND_URL=http://localhost:3000

# JWT Configuration
JWT_SECRET=lapromise_school_jwt_secret_key_2024
JWT_EXPIRES_IN=7d

# School Information
SCHOOL_NAME=La Promise School
SCHOOL_LOCATION=Gisenyi, Rubavu, Rwanda
SCHOOL_MOTTO=Discipline, Work, Cleanliness
SCHOOL_EMAIL=<EMAIL>
SCHOOL_PHONE=+250 788 123 456
```

#### Setup Database
Make sure MySQL is running and create the database:
```sql
CREATE DATABASE lapromise_school;
```

#### Start the backend server
```bash
npm run dev
```

#### Seed the database (optional)
```bash
npm run seed
```

### 3. Frontend Setup

#### Navigate to frontend directory (in a new terminal)
```bash
cd frontend_lapromise
```

#### Install dependencies
```bash
npm install
```

#### Start the frontend development server
```bash
npm start
```

## 🌐 Access the Application

- **Frontend**: http://localhost:3000
- **Backend API**: http://localhost:5000
- **API Health Check**: http://localhost:5000/api/health

## 📱 Features

### Public Features
- **Home Page** - Welcome message, school information, and announcements
- **About Page** - School history, mission, vision, and values
- **Programs Page** - Academic programs and curriculum details
- **Gallery Page** - School photos and events
- **Contact Page** - Contact form and school information

### API Features
- **Announcements Management** - CRUD operations for school announcements
- **Contact Form** - Handle contact form submissions
- **Student Management** - Student records and information
- **Teacher Management** - Teacher profiles and details
- **Course Management** - Academic courses and subjects
- **Gallery Management** - Photo gallery management

## 🎨 Design Features

- **Blue and White Color Scheme** - Professional and clean design
- **Responsive Design** - Works on all devices
- **Modern UI Components** - Built with Tailwind CSS
- **Smooth Animations** - Enhanced user experience
- **Accessibility** - WCAG compliant design

## 📊 Database Schema

The application uses the following main tables:
- `users` - User authentication and roles
- `students` - Student information and records
- `teachers` - Teacher profiles and details
- `courses` - Academic courses and subjects
- `announcements` - School announcements and news
- `contact_messages` - Contact form submissions
- `gallery` - Photo gallery items

## 🔧 Available Scripts

### Backend Scripts
- `npm start` - Start production server
- `npm run dev` - Start development server with nodemon
- `npm run seed` - Seed database with sample data

### Frontend Scripts
- `npm start` - Start development server
- `npm run build` - Build for production
- `npm test` - Run tests

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit your changes (`git commit -m 'Add some AmazingFeature'`)
4. Push to the branch (`git push origin feature/AmazingFeature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the ISC License.

## 📞 Contact

**La Promise School**
- 📍 Location: Gisenyi, Rubavu District, Western Province, Rwanda
- 📞 Phone: +250 788 123 456
- 📧 Email: <EMAIL>
- 🌐 Website: [Your Website URL]

---

**Built with ❤️ for La Promise School - Shaping a brighter future for Rwanda through quality education.**
