import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { 
  MapPinIcon, 
  PhoneIcon, 
  EnvelopeIcon,
  AcademicCapIcon
} from '@heroicons/react/24/outline';

const Footer: React.FC = () => {
  return (
    <footer className="bg-secondary-900 text-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* School Info */}
          <div className="space-y-4">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-primary-600 rounded-full flex items-center justify-center">
                <AcademicCapIcon className="h-6 w-6 text-white" />
              </div>
              <div>
                <h3 className="text-lg font-heading font-bold">La Promise School</h3>
                <p className="text-secondary-300 text-sm">Excellence in Education</p>
              </div>
            </div>
            <p className="text-secondary-300 text-sm leading-relaxed">
              Shaping a brighter future for Rwanda through quality education, 
              innovative teaching, and character development in Gisenyi, Rubavu.
            </p>
          </div>

          {/* Quick Links */}
          <div>
            <h4 className="text-lg font-semibold mb-4">Quick Links</h4>
            <ul className="space-y-2">
              <li>
                <Link to="/" className="text-secondary-300 hover:text-white transition-colors duration-200">
                  Home
                </Link>
              </li>
              <li>
                <Link to="/about" className="text-secondary-300 hover:text-white transition-colors duration-200">
                  About Us
                </Link>
              </li>
              <li>
                <Link to="/programs" className="text-secondary-300 hover:text-white transition-colors duration-200">
                  Academic Programs
                </Link>
              </li>
              <li>
                <Link to="/gallery" className="text-secondary-300 hover:text-white transition-colors duration-200">
                  Gallery
                </Link>
              </li>
              <li>
                <Link to="/contact" className="text-secondary-300 hover:text-white transition-colors duration-200">
                  Contact Us
                </Link>
              </li>
            </ul>
          </div>

          {/* Academic Programs */}
          <div>
            <h4 className="text-lg font-semibold mb-4">Programs</h4>
            <ul className="space-y-2 text-secondary-300 text-sm">
              <li>Nursery Education (Ages 3-5)</li>
              <li>Primary Education (Grades 1-6)</li>
              <li>Language Development</li>
              <li>Creative Arts</li>
              <li>Physical Activities</li>
              <li>Life Skills</li>
            </ul>
          </div>

          {/* Contact Info */}
          <div>
            <h4 className="text-lg font-semibold mb-4">Contact Info</h4>
            <div className="space-y-3">
              <div className="flex items-start space-x-3">
                <MapPinIcon className="h-5 w-5 text-primary-400 mt-0.5 flex-shrink-0" />
                <div>
                  <p className="text-secondary-300 text-sm">
                    Gisenyi, Rubavu District<br />
                    Western Province, Rwanda
                  </p>
                </div>
              </div>
              <div className="flex items-center space-x-3">
                <PhoneIcon className="h-5 w-5 text-primary-400 flex-shrink-0" />
                <p className="text-secondary-300 text-sm">+250 788 123 456</p>
              </div>
              <div className="flex items-center space-x-3">
                <EnvelopeIcon className="h-5 w-5 text-primary-400 flex-shrink-0" />
                <p className="text-secondary-300 text-sm"><EMAIL></p>
              </div>
            </div>
          </div>
        </div>

        {/* Bottom Section */}
        <div className="border-t border-secondary-700 mt-8 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="text-secondary-300 text-sm mb-4 md:mb-0">
              <p>&copy; 2024 La Promise School. All rights reserved.</p>
            </div>
            <div className="text-secondary-300 text-sm">
              <p className="font-medium">🎯 Discipline • Work • Cleanliness</p>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
