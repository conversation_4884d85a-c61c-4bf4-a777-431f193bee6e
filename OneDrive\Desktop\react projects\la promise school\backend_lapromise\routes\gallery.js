const express = require('express');
const { pool } = require('../config/database');
const router = express.Router();

// Get all gallery items
router.get('/', async (req, res) => {
  try {
    const { category, limit = 20, offset = 0 } = req.query;

    let query = `
      SELECT g.*, u.username as uploaded_by_name 
      FROM gallery g 
      LEFT JOIN users u ON g.uploaded_by = u.id 
      WHERE g.status = 'active'
    `;
    const params = [];

    if (category) {
      query += ' AND g.category = ?';
      params.push(category);
    }

    query += ' ORDER BY g.created_at DESC LIMIT ? OFFSET ?';
    params.push(parseInt(limit), parseInt(offset));

    const [gallery] = await pool.execute(query, params);

    res.json({ gallery });

  } catch (error) {
    console.error('Error fetching gallery:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Add new gallery item
router.post('/', async (req, res) => {
  try {
    const {
      title,
      description,
      image_url,
      category = 'events',
      uploaded_by = 1 // Default admin user
    } = req.body;

    if (!title || !image_url) {
      return res.status(400).json({ message: 'Title and image URL are required' });
    }

    const [result] = await pool.execute(`
      INSERT INTO gallery (title, description, image_url, category, uploaded_by) 
      VALUES (?, ?, ?, ?, ?)
    `, [title, description, image_url, category, uploaded_by]);

    res.status(201).json({
      message: 'Gallery item added successfully',
      gallery: { id: result.insertId, title, image_url, category }
    });

  } catch (error) {
    console.error('Error adding gallery item:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

module.exports = router;
