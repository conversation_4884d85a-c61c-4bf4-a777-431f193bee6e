const express = require('express');
const { pool } = require('../config/database');
const router = express.Router();

// Get all courses
router.get('/', async (req, res) => {
  try {
    const { class_level, status = 'active', limit = 50, offset = 0 } = req.query;

    let query = `
      SELECT c.*, t.first_name as teacher_first_name, t.last_name as teacher_last_name 
      FROM courses c 
      LEFT JOIN teachers t ON c.teacher_id = t.id 
      WHERE c.status = ?
    `;
    const params = [status];

    if (class_level) {
      query += ' AND c.class_level = ?';
      params.push(class_level);
    }

    query += ' ORDER BY c.class_level, c.course_name LIMIT ? OFFSET ?';
    params.push(parseInt(limit), parseInt(offset));

    const [courses] = await pool.execute(query, params);

    res.json({ courses });

  } catch (error) {
    console.error('Error fetching courses:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Create new course
router.post('/', async (req, res) => {
  try {
    const {
      course_code,
      course_name,
      description,
      class_level,
      teacher_id,
      credits
    } = req.body;

    if (!course_code || !course_name || !class_level) {
      return res.status(400).json({ 
        message: 'Course code, name, and class level are required' 
      });
    }

    const [result] = await pool.execute(`
      INSERT INTO courses (course_code, course_name, description, class_level, teacher_id, credits) 
      VALUES (?, ?, ?, ?, ?, ?)
    `, [course_code, course_name, description, class_level, teacher_id, credits || 1]);

    res.status(201).json({
      message: 'Course created successfully',
      course: { id: result.insertId, course_code, course_name, class_level }
    });

  } catch (error) {
    console.error('Error creating course:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

module.exports = router;
