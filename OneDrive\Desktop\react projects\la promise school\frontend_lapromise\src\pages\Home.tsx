import React from 'react';
import { Link } from 'react-router-dom';
import {
  AcademicCapIcon,
  UsersIcon,
  TrophyIcon,
  BookOpenIcon,
  GlobeAltIcon,
  HeartIcon
} from '@heroicons/react/24/outline';
import AnnouncementsList from '../components/AnnouncementsList';

const Home: React.FC = () => {
  const features = [
    {
      icon: AcademicCapIcon,
      title: 'Quality Education',
      description: 'High-quality, student-centered education with modern teaching methods and resources.'
    },
    {
      icon: UsersIcon,
      title: 'Experienced Teachers',
      description: 'Dedicated and qualified teachers committed to nurturing every student\'s potential.'
    },
    {
      icon: BookOpenIcon,
      title: 'Modern Curriculum',
      description: 'Comprehensive curriculum that encourages critical thinking and creativity.'
    },
    {
      icon: TrophyIcon,
      title: 'Excellence',
      description: 'Committed to academic excellence and character development for all students.'
    },
    {
      icon: GlobeAltIcon,
      title: 'Global Perspective',
      description: 'Preparing students to become responsible global citizens and leaders.'
    },
    {
      icon: HeartIcon,
      title: 'Supportive Environment',
      description: 'Nurturing and supportive environment that promotes holistic development.'
    }
  ];

  const stats = [
    { number: '500+', label: 'Students' },
    { number: '25+', label: 'Teachers' },
    { number: '15+', label: 'Years of Excellence' },
    { number: '95%', label: 'Success Rate' }
  ];

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative bg-gradient-to-br from-primary-600 via-primary-700 to-primary-800 text-white">
        <div className="absolute inset-0 bg-black opacity-20"></div>
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24 lg:py-32">
          <div className="text-center">
            <h1 className="text-4xl md:text-6xl font-heading font-bold mb-6 animate-fade-in">
              Welcome to <span className="text-yellow-300">La Promise School</span>
            </h1>
            <p className="text-xl md:text-2xl mb-8 max-w-3xl mx-auto leading-relaxed animate-slide-up">
              Shaping a brighter future for Rwanda through high-quality, student-centered education 
              in the heart of Gisenyi, Rubavu.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center animate-slide-up">
              <Link
                to="/about"
                className="bg-white text-primary-700 px-8 py-3 rounded-lg font-semibold hover:bg-primary-50 transition-colors duration-200 shadow-lg"
              >
                Learn More About Us
              </Link>
              <Link
                to="/contact"
                className="border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-primary-700 transition-colors duration-200"
              >
                Get In Touch
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Mission Statement */}
      <section className="py-16 bg-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl md:text-4xl font-heading font-bold text-secondary-900 mb-8">
            Our Mission
          </h2>
          <p className="text-lg text-secondary-700 leading-relaxed">
            At La Promise School, we are committed to shaping a brighter future for Rwanda by providing 
            high-quality, student-centered education. We nurture learners through innovative teaching, 
            modern resources, and a supportive environment that encourages critical thinking, creativity, 
            and character development. Our mission is to empower every student to reach their full potential 
            and become responsible citizens ready to contribute to their communities and beyond.
          </p>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-16 bg-secondary-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-heading font-bold text-secondary-900 mb-4">
              Why Choose La Promise School?
            </h2>
            <p className="text-lg text-secondary-600 max-w-2xl mx-auto">
              We provide exceptional education with a focus on discipline, work, and cleanliness.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <div 
                key={index}
                className="bg-white p-6 rounded-xl shadow-lg hover:shadow-xl transition-shadow duration-300"
              >
                <div className="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center mb-4">
                  <feature.icon className="h-6 w-6 text-primary-600" />
                </div>
                <h3 className="text-xl font-semibold text-secondary-900 mb-3">
                  {feature.title}
                </h3>
                <p className="text-secondary-600">
                  {feature.description}
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-16 bg-primary-600 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-heading font-bold mb-4">
              Our Achievements
            </h2>
            <p className="text-xl text-primary-100">
              Numbers that reflect our commitment to excellence
            </p>
          </div>
          
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <div key={index} className="text-center">
                <div className="text-4xl md:text-5xl font-bold mb-2">
                  {stat.number}
                </div>
                <div className="text-primary-200 font-medium">
                  {stat.label}
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Announcements Section */}
      <section className="py-16 bg-secondary-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <AnnouncementsList />
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl md:text-4xl font-heading font-bold text-secondary-900 mb-6">
            Ready to Join Our Community?
          </h2>
          <p className="text-lg text-secondary-600 mb-8">
            Discover how La Promise School can help your child reach their full potential
            in a nurturing and supportive environment.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              to="/programs"
              className="bg-primary-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-primary-700 transition-colors duration-200 shadow-lg"
            >
              Explore Programs
            </Link>
            <Link
              to="/contact"
              className="border-2 border-primary-600 text-primary-600 px-8 py-3 rounded-lg font-semibold hover:bg-primary-600 hover:text-white transition-colors duration-200"
            >
              Contact Us Today
            </Link>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Home;
