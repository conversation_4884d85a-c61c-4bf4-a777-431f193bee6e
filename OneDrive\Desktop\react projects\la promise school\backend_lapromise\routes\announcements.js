const express = require('express');
const { pool } = require('../config/database');
const router = express.Router();

// Get all announcements (public)
router.get('/', async (req, res) => {
  try {
    const { limit = 10, offset = 0, target_audience = 'all' } = req.query;

    let query = `
      SELECT a.*, u.username as author_name 
      FROM announcements a 
      LEFT JOIN users u ON a.author_id = u.id 
      WHERE a.status = 'published' 
      AND (a.expiry_date IS NULL OR a.expiry_date > NOW())
    `;
    
    const params = [];

    if (target_audience !== 'all') {
      query += ' AND (a.target_audience = ? OR a.target_audience = "all")';
      params.push(target_audience);
    }

    query += ' ORDER BY a.priority DESC, a.publish_date DESC LIMIT ? OFFSET ?';
    params.push(parseInt(limit), parseInt(offset));

    const [announcements] = await pool.execute(query, params);

    res.json({
      announcements,
      total: announcements.length,
      limit: parseInt(limit),
      offset: parseInt(offset)
    });

  } catch (error) {
    console.error('Error fetching announcements:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Get single announcement by ID
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;

    const [announcements] = await pool.execute(`
      SELECT a.*, u.username as author_name 
      FROM announcements a 
      LEFT JOIN users u ON a.author_id = u.id 
      WHERE a.id = ? AND a.status = 'published'
    `, [id]);

    if (announcements.length === 0) {
      return res.status(404).json({ message: 'Announcement not found' });
    }

    res.json({ announcement: announcements[0] });

  } catch (error) {
    console.error('Error fetching announcement:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Create new announcement (admin/teacher only)
router.post('/', async (req, res) => {
  try {
    const {
      title,
      content,
      target_audience = 'all',
      priority = 'medium',
      publish_date,
      expiry_date,
      image
    } = req.body;

    // Basic validation
    if (!title || !content) {
      return res.status(400).json({ message: 'Title and content are required' });
    }

    // For now, we'll use a default author_id of 1 (admin)
    // In a real app, this would come from the authenticated user
    const author_id = 1;

    const [result] = await pool.execute(`
      INSERT INTO announcements 
      (title, content, author_id, target_audience, priority, publish_date, expiry_date, image) 
      VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    `, [
      title,
      content,
      author_id,
      target_audience,
      priority,
      publish_date || new Date(),
      expiry_date,
      image
    ]);

    res.status(201).json({
      message: 'Announcement created successfully',
      announcement: {
        id: result.insertId,
        title,
        content,
        target_audience,
        priority,
        publish_date: publish_date || new Date(),
        expiry_date,
        image
      }
    });

  } catch (error) {
    console.error('Error creating announcement:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Update announcement
router.put('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const {
      title,
      content,
      target_audience,
      priority,
      status,
      expiry_date,
      image
    } = req.body;

    // Check if announcement exists
    const [existing] = await pool.execute(
      'SELECT id FROM announcements WHERE id = ?',
      [id]
    );

    if (existing.length === 0) {
      return res.status(404).json({ message: 'Announcement not found' });
    }

    // Build update query dynamically
    const updates = [];
    const params = [];

    if (title) {
      updates.push('title = ?');
      params.push(title);
    }
    if (content) {
      updates.push('content = ?');
      params.push(content);
    }
    if (target_audience) {
      updates.push('target_audience = ?');
      params.push(target_audience);
    }
    if (priority) {
      updates.push('priority = ?');
      params.push(priority);
    }
    if (status) {
      updates.push('status = ?');
      params.push(status);
    }
    if (expiry_date !== undefined) {
      updates.push('expiry_date = ?');
      params.push(expiry_date);
    }
    if (image !== undefined) {
      updates.push('image = ?');
      params.push(image);
    }

    if (updates.length === 0) {
      return res.status(400).json({ message: 'No fields to update' });
    }

    params.push(id);

    await pool.execute(
      `UPDATE announcements SET ${updates.join(', ')}, updated_at = CURRENT_TIMESTAMP WHERE id = ?`,
      params
    );

    res.json({ message: 'Announcement updated successfully' });

  } catch (error) {
    console.error('Error updating announcement:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Delete announcement
router.delete('/:id', async (req, res) => {
  try {
    const { id } = req.params;

    const [result] = await pool.execute(
      'DELETE FROM announcements WHERE id = ?',
      [id]
    );

    if (result.affectedRows === 0) {
      return res.status(404).json({ message: 'Announcement not found' });
    }

    res.json({ message: 'Announcement deleted successfully' });

  } catch (error) {
    console.error('Error deleting announcement:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

module.exports = router;
