import React, { useState } from 'react';

const Gallery: React.FC = () => {
  const [activeCategory, setActiveCategory] = useState('all');

  const categories = [
    { id: 'all', name: 'All Photos' },
    { id: 'campus', name: 'Campus' },
    { id: 'students', name: 'Students' },
    { id: 'events', name: 'Events' },
    { id: 'activities', name: 'Activities' },
    { id: 'facilities', name: 'Facilities' }
  ];

  // Sample gallery data - in a real app, this would come from your backend
  const galleryItems = [
    {
      id: 1,
      title: 'School Main Building',
      category: 'campus',
      image: 'https://images.unsplash.com/photo-1580582932707-520aed937b7b?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      description: 'Our beautiful main academic building'
    },
    {
      id: 2,
      title: 'Students in Science Lab',
      category: 'students',
      image: 'https://images.unsplash.com/photo-1532094349884-543bc11b234d?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      description: 'Students conducting experiments in our modern science laboratory'
    },
    {
      id: 3,
      title: 'Annual Sports Day',
      category: 'events',
      image: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      description: 'Students participating in our annual sports day celebration'
    },
    {
      id: 4,
      title: 'Library Study Area',
      category: 'facilities',
      image: 'https://images.unsplash.com/photo-1481627834876-b7833e8f5570?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      description: 'Our well-equipped library provides a quiet space for study'
    },
    {
      id: 5,
      title: 'Art Class Activity',
      category: 'activities',
      image: 'https://images.unsplash.com/photo-1513475382585-d06e58bcb0e0?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      description: 'Students expressing creativity in our art classes'
    },
    {
      id: 6,
      title: 'Computer Laboratory',
      category: 'facilities',
      image: 'https://images.unsplash.com/photo-1498050108023-c5249f4df085?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      description: 'Modern computer lab with internet access for all students'
    },
    {
      id: 7,
      title: 'Graduation Ceremony',
      category: 'events',
      image: 'https://images.unsplash.com/photo-1523050854058-8df90110c9d1?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      description: 'Celebrating our graduates and their achievements'
    },
    {
      id: 8,
      title: 'Music Performance',
      category: 'activities',
      image: 'https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      description: 'Students showcasing their musical talents'
    },
    {
      id: 9,
      title: 'School Playground',
      category: 'campus',
      image: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      description: 'Safe and fun playground area for our younger students'
    }
  ];

  const filteredItems = activeCategory === 'all' 
    ? galleryItems 
    : galleryItems.filter(item => item.category === activeCategory);

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-primary-600 to-primary-800 text-white py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-4xl md:text-5xl font-heading font-bold mb-6">
              School Gallery
            </h1>
            <p className="text-xl md:text-2xl max-w-3xl mx-auto">
              Take a visual journey through La Promise School - our campus, 
              students, events, and the vibrant learning community we've built.
            </p>
          </div>
        </div>
      </section>

      {/* Gallery Section */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Category Filter */}
          <div className="flex flex-wrap justify-center gap-4 mb-12">
            {categories.map((category) => (
              <button
                key={category.id}
                onClick={() => setActiveCategory(category.id)}
                className={`px-6 py-2 rounded-full font-medium transition-colors duration-200 ${
                  activeCategory === category.id
                    ? 'bg-primary-600 text-white'
                    : 'bg-secondary-100 text-secondary-700 hover:bg-primary-100 hover:text-primary-700'
                }`}
              >
                {category.name}
              </button>
            ))}
          </div>

          {/* Gallery Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {filteredItems.map((item) => (
              <div 
                key={item.id}
                className="group relative overflow-hidden rounded-xl shadow-lg hover:shadow-xl transition-shadow duration-300"
              >
                <div className="aspect-w-4 aspect-h-3">
                  <img
                    src={item.image}
                    alt={item.title}
                    className="w-full h-64 object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                </div>
                <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                  <div className="absolute bottom-0 left-0 right-0 p-6 text-white">
                    <h3 className="text-lg font-semibold mb-2">{item.title}</h3>
                    <p className="text-sm text-gray-200">{item.description}</p>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* No items message */}
          {filteredItems.length === 0 && (
            <div className="text-center py-12">
              <p className="text-secondary-600 text-lg">
                No photos found in this category. Please check back later!
              </p>
            </div>
          )}
        </div>
      </section>

      {/* School Highlights */}
      <section className="py-16 bg-secondary-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-heading font-bold text-secondary-900 mb-4">
              Life at La Promise School
            </h2>
            <p className="text-lg text-secondary-600 max-w-2xl mx-auto">
              Our gallery captures the essence of daily life at our school - 
              from academic excellence to creative expression and community spirit.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl">📚</span>
              </div>
              <h3 className="text-xl font-semibold text-secondary-900 mb-3">Academic Excellence</h3>
              <p className="text-secondary-600">
                Modern classrooms, well-equipped laboratories, and innovative teaching methods 
                that inspire learning and discovery.
              </p>
            </div>
            
            <div className="text-center">
              <div className="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl">🎨</span>
              </div>
              <h3 className="text-xl font-semibold text-secondary-900 mb-3">Creative Expression</h3>
              <p className="text-secondary-600">
                Art, music, drama, and cultural activities that nurture creativity 
                and help students discover their unique talents.
              </p>
            </div>
            
            <div className="text-center">
              <div className="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl">🤝</span>
              </div>
              <h3 className="text-xl font-semibold text-secondary-900 mb-3">Community Spirit</h3>
              <p className="text-secondary-600">
                Events, celebrations, and activities that bring our school community 
                together and create lasting memories.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-primary-600 text-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl md:text-4xl font-heading font-bold mb-6">
            Want to Be Part of Our Story?
          </h2>
          <p className="text-xl text-primary-100 mb-8">
            Join La Promise School and create your own memorable moments in our vibrant learning community.
          </p>
          <a
            href="/contact"
            className="bg-white text-primary-600 px-8 py-3 rounded-lg font-semibold hover:bg-primary-50 transition-colors duration-200 shadow-lg inline-block"
          >
            Contact Us Today
          </a>
        </div>
      </section>
    </div>
  );
};

export default Gallery;
