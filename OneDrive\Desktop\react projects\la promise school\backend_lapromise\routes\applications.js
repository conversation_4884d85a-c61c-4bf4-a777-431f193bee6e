const express = require('express');
const { pool } = require('../config/database');
const router = express.Router();

// Get all applications (admin only)
router.get('/', async (req, res) => {
  try {
    const { status, limit = 50, offset = 0 } = req.query;

    let query = `
      SELECT * FROM applications 
      WHERE 1=1
    `;
    const params = [];

    if (status) {
      query += ' AND status = ?';
      params.push(status);
    }

    query += ' ORDER BY created_at DESC LIMIT ? OFFSET ?';
    params.push(parseInt(limit), parseInt(offset));

    const [applications] = await pool.execute(query, params);

    res.json({
      applications,
      total: applications.length,
      limit: parseInt(limit),
      offset: parseInt(offset)
    });

  } catch (error) {
    console.error('Error fetching applications:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Get single application by ID
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;

    const [applications] = await pool.execute(
      'SELECT * FROM applications WHERE id = ?',
      [id]
    );

    if (applications.length === 0) {
      return res.status(404).json({ message: 'Application not found' });
    }

    res.json(applications[0]);

  } catch (error) {
    console.error('Error fetching application:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Submit new application
router.post('/', async (req, res) => {
  try {
    const {
      // Student Information
      firstName,
      lastName,
      dateOfBirth,
      gender,
      nationality,
      placeOfBirth,
      
      // Contact Information
      address,
      city,
      state,
      postalCode,
      phone,
      email,
      
      // Parent/Guardian Information
      parentFirstName,
      parentLastName,
      parentRelationship,
      parentPhone,
      parentEmail,
      parentOccupation,
      parentAddress,
      
      // Emergency Contact
      emergencyContactName,
      emergencyContactPhone,
      emergencyContactRelationship,
      
      // Academic Information
      previousSchool,
      previousSchoolAddress,
      lastGradeCompleted,
      desiredGradeLevel,
      academicYear,
      
      // Medical Information
      medicalConditions,
      allergies,
      medications,
      
      // Additional Information
      specialNeeds,
      extracurricularInterests,
      reasonForApplying
    } = req.body;

    // Validation
    if (!firstName || !lastName || !dateOfBirth || !gender || !phone || !email || 
        !parentFirstName || !parentLastName || !parentPhone || !parentEmail || 
        !emergencyContactName || !emergencyContactPhone || !desiredGradeLevel || 
        !reasonForApplying) {
      return res.status(400).json({ 
        message: 'Required fields are missing. Please fill in all required information.' 
      });
    }

    // Email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email) || !emailRegex.test(parentEmail)) {
      return res.status(400).json({ message: 'Invalid email format' });
    }

    // Generate application number
    const applicationNumber = 'APP' + Date.now();

    const [result] = await pool.execute(`
      INSERT INTO applications (
        application_number,
        first_name,
        last_name,
        date_of_birth,
        gender,
        nationality,
        place_of_birth,
        address,
        city,
        state,
        postal_code,
        phone,
        email,
        parent_first_name,
        parent_last_name,
        parent_relationship,
        parent_phone,
        parent_email,
        parent_occupation,
        parent_address,
        emergency_contact_name,
        emergency_contact_phone,
        emergency_contact_relationship,
        previous_school,
        previous_school_address,
        last_grade_completed,
        desired_grade_level,
        academic_year,
        medical_conditions,
        allergies,
        medications,
        special_needs,
        extracurricular_interests,
        reason_for_applying,
        status
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `, [
      applicationNumber,
      firstName,
      lastName,
      dateOfBirth,
      gender,
      nationality || null,
      placeOfBirth || null,
      address,
      city,
      state || null,
      postalCode || null,
      phone,
      email,
      parentFirstName,
      parentLastName,
      parentRelationship,
      parentPhone,
      parentEmail,
      parentOccupation || null,
      parentAddress || null,
      emergencyContactName,
      emergencyContactPhone,
      emergencyContactRelationship,
      previousSchool || null,
      previousSchoolAddress || null,
      lastGradeCompleted || null,
      desiredGradeLevel,
      academicYear,
      medicalConditions || null,
      allergies || null,
      medications || null,
      specialNeeds || null,
      extracurricularInterests || null,
      reasonForApplying,
      'pending'
    ]);

    res.status(201).json({
      message: 'Application submitted successfully',
      application: {
        id: result.insertId,
        applicationNumber,
        firstName,
        lastName,
        email,
        status: 'pending'
      }
    });

  } catch (error) {
    console.error('Error submitting application:', error);
    res.status(500).json({ message: 'Server error while submitting application' });
  }
});

// Update application status (admin only)
router.put('/:id/status', async (req, res) => {
  try {
    const { id } = req.params;
    const { status, notes } = req.body;

    // Validate status
    const validStatuses = ['pending', 'under_review', 'approved', 'rejected', 'waitlisted'];
    if (!validStatuses.includes(status)) {
      return res.status(400).json({ message: 'Invalid status' });
    }

    // Check if application exists
    const [existing] = await pool.execute(
      'SELECT id FROM applications WHERE id = ?',
      [id]
    );

    if (existing.length === 0) {
      return res.status(404).json({ message: 'Application not found' });
    }

    // Update application status
    await pool.execute(
      'UPDATE applications SET status = ?, admin_notes = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
      [status, notes || null, id]
    );

    res.json({
      message: 'Application status updated successfully',
      status,
      notes
    });

  } catch (error) {
    console.error('Error updating application status:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Delete application (admin only)
router.delete('/:id', async (req, res) => {
  try {
    const { id } = req.params;

    // Check if application exists
    const [existing] = await pool.execute(
      'SELECT id FROM applications WHERE id = ?',
      [id]
    );

    if (existing.length === 0) {
      return res.status(404).json({ message: 'Application not found' });
    }

    // Delete application
    await pool.execute('DELETE FROM applications WHERE id = ?', [id]);

    res.json({ message: 'Application deleted successfully' });

  } catch (error) {
    console.error('Error deleting application:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Get application statistics (admin only)
router.get('/stats/overview', async (req, res) => {
  try {
    const [stats] = await pool.execute(`
      SELECT 
        COUNT(*) as total,
        SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending,
        SUM(CASE WHEN status = 'under_review' THEN 1 ELSE 0 END) as under_review,
        SUM(CASE WHEN status = 'approved' THEN 1 ELSE 0 END) as approved,
        SUM(CASE WHEN status = 'rejected' THEN 1 ELSE 0 END) as rejected,
        SUM(CASE WHEN status = 'waitlisted' THEN 1 ELSE 0 END) as waitlisted
      FROM applications
    `);

    res.json(stats[0]);

  } catch (error) {
    console.error('Error fetching application statistics:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

module.exports = router;
